// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCUHcD1lE_pRHm1iGr5_9gpVq6AzN805nQ',
    appId: '1:685587131461:web:2c5ecea7bb11da97dac72c',
    messagingSenderId: '685587131461',
    projectId: 'ambient-flow-d9e78',
    authDomain: 'ambient-flow-d9e78.firebaseapp.com',
    storageBucket: 'ambient-flow-d9e78.firebasestorage.app',
    measurementId: 'G-QGEHWJR4VS',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyD1Me73m6zGlC4gNYR9wyKnLauj_kKSDMY',
    appId: '1:685587131461:android:3549201f4e3b46c8dac72c',
    messagingSenderId: '685587131461',
    projectId: 'ambient-flow-d9e78',
    storageBucket: 'ambient-flow-d9e78.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAQK65Nd4eMxT2E_6avqwNb462UMV8IDx8',
    appId: '1:685587131461:ios:334bc0a577ca4ee9dac72c',
    messagingSenderId: '685587131461',
    projectId: 'ambient-flow-d9e78',
    storageBucket: 'ambient-flow-d9e78.firebasestorage.app',
    iosClientId: '685587131461-v4q94mio7gmhqm8456gka4dcgft6qi23.apps.googleusercontent.com',
    iosBundleId: 'com.colossus.ambientflow.ambientflow',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAQK65Nd4eMxT2E_6avqwNb462UMV8IDx8',
    appId: '1:685587131461:ios:334bc0a577ca4ee9dac72c',
    messagingSenderId: '685587131461',
    projectId: 'ambient-flow-d9e78',
    storageBucket: 'ambient-flow-d9e78.firebasestorage.app',
    iosClientId: '685587131461-v4q94mio7gmhqm8456gka4dcgft6qi23.apps.googleusercontent.com',
    iosBundleId: 'com.colossus.ambientflow.ambientflow',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'AIzaSyCUHcD1lE_pRHm1iGr5_9gpVq6AzN805nQ',
    appId: '1:685587131461:web:42310fa99d201d87dac72c',
    messagingSenderId: '685587131461',
    projectId: 'ambient-flow-d9e78',
    authDomain: 'ambient-flow-d9e78.firebaseapp.com',
    storageBucket: 'ambient-flow-d9e78.firebasestorage.app',
    measurementId: 'G-DBMZWBNKHT',
  );
}
